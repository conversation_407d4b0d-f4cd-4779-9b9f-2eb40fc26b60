<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Select Coin Filter Mockup</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .dropdown-menu {
            max-height: 300px;
            overflow-y: auto;
        }
        .coin-badge {
            background: #3b82f6;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            margin: 2px;
        }
        .coin-badge .remove {
            cursor: pointer;
            font-weight: bold;
            opacity: 0.7;
        }
        .coin-badge .remove:hover {
            opacity: 1;
        }
    </style>
</head>
<body class="bg-gray-900 text-white p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Multi-Select Coin Filter Mockup</h1>
        
        <!-- Current vs New Comparison -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            
            <!-- Current Single Select -->
            <div class="bg-gray-800 p-6 rounded-lg">
                <h2 class="text-lg font-semibold mb-4 text-blue-400">Current (Single Select)</h2>
                <div class="flex items-center">
                    <button class="bg-gray-50 text-gray-900 w-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 text-sm rounded-l-lg flex items-center justify-center p-2.5">
                        🪙
                    </button>
                    <div class="relative">
                        <select class="bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 text-sm rounded-r-lg outline-none block w-full p-2.5">
                            <option>Filter Offers</option>
                            <option>Bitcoin</option>
                            <option>Monero</option>
                            <option>Particl</option>
                        </select>
                        <div class="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">▼</div>
                    </div>
                </div>
            </div>

            <!-- New Multi-Select -->
            <div class="bg-gray-800 p-6 rounded-lg">
                <h2 class="text-lg font-semibold mb-4 text-green-400">New (Multi-Select)</h2>
                <div class="relative">
                    <div class="flex items-center">
                        <button class="bg-gray-50 text-gray-900 w-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 text-sm rounded-l-lg flex items-center justify-center p-2.5">
                            🪙
                        </button>
                        <button id="multiSelectButton" class="bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 text-sm rounded-r-lg outline-none block w-full p-2.5 text-left">
                            Filter Offers (2 selected)
                        </button>
                        <div class="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">▼</div>
                    </div>
                    
                    <!-- Multi-Select Dropdown -->
                    <div id="multiSelectDropdown" class="absolute top-full left-0 right-0 mt-1 bg-gray-500 border border-gray-400 rounded-lg shadow-lg z-50 dropdown-menu">
                        <div class="p-2">
                            <label class="flex items-center p-2 hover:bg-gray-600 rounded cursor-pointer">
                                <input type="checkbox" class="mr-3" onchange="clearAll(this)">
                                <span class="text-sm">Any (clear all)</span>
                            </label>
                            <label class="flex items-center p-2 hover:bg-gray-600 rounded cursor-pointer">
                                <input type="checkbox" class="mr-3" checked>
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSI+PGNpcmNsZSBjeD0iMTAiIGN5PSIxMCIgcj0iMTAiIGZpbGw9IiNmN2Y3ZjciLz48L3N2Zz4=" class="w-4 h-4 mr-2" alt="Bitcoin">
                                <span class="text-sm">Bitcoin</span>
                            </label>
                            <label class="flex items-center p-2 hover:bg-gray-600 rounded cursor-pointer">
                                <input type="checkbox" class="mr-3">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSI+PGNpcmNsZSBjeD0iMTAiIGN5PSIxMCIgcj0iMTAiIGZpbGw9IiNmZjY2MDAiLz48L3N2Zz4=" class="w-4 h-4 mr-2" alt="Monero">
                                <span class="text-sm">Monero</span>
                            </label>
                            <label class="flex items-center p-2 hover:bg-gray-600 rounded cursor-pointer">
                                <input type="checkbox" class="mr-3" checked>
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSI+PGNpcmNsZSBjeD0iMTAiIGN5PSIxMCIgcj0iMTAiIGZpbGw9IiMwMGQ0YWEiLz48L3N2Zz4=" class="w-4 h-4 mr-2" alt="Particl">
                                <span class="text-sm">Particl</span>
                            </label>
                            <label class="flex items-center p-2 hover:bg-gray-600 rounded cursor-pointer">
                                <input type="checkbox" class="mr-3">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSI+PGNpcmNsZSBjeD0iMTAiIGN5PSIxMCIgcj0iMTAiIGZpbGw9IiNjY2NjY2MiLz48L3N2Zz4=" class="w-4 h-4 mr-2" alt="Litecoin">
                                <span class="text-sm">Litecoin</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- Selected Coins Badges -->
                <div class="mt-3">
                    <div class="text-xs text-gray-400 mb-1">Selected:</div>
                    <div class="flex flex-wrap">
                        <span class="coin-badge">
                            Bitcoin <span class="remove" onclick="removeCoin('bitcoin')">×</span>
                        </span>
                        <span class="coin-badge">
                            Particl <span class="remove" onclick="removeCoin('particl')">×</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features List -->
        <div class="bg-gray-800 p-6 rounded-lg">
            <h2 class="text-lg font-semibold mb-4 text-yellow-400">Key Features</h2>
            <ul class="space-y-2 text-sm">
                <li class="flex items-center"><span class="text-green-400 mr-2">✓</span> Checkboxes for multiple selection</li>
                <li class="flex items-center"><span class="text-green-400 mr-2">✓</span> Count displayed in button text</li>
                <li class="flex items-center"><span class="text-green-400 mr-2">✓</span> Removable badges for selected coins</li>
                <li class="flex items-center"><span class="text-green-400 mr-2">✓</span> "Any" option clears all selections</li>
                <li class="flex items-center"><span class="text-green-400 mr-2">✓</span> Maintains current visual style and layout</li>
                <li class="flex items-center"><span class="text-green-400 mr-2">✓</span> Coin icons preserved in dropdown</li>
                <li class="flex items-center"><span class="text-green-400 mr-2">✓</span> Hover effects and accessibility</li>
            </ul>
        </div>

        <!-- Implementation Notes -->
        <div class="bg-gray-800 p-6 rounded-lg mt-6">
            <h2 class="text-lg font-semibold mb-4 text-purple-400">Implementation Notes</h2>
            <ul class="space-y-2 text-sm text-gray-300">
                <li>• Backend will receive array of coin IDs instead of single value</li>
                <li>• JavaScript filtering logic updated to handle multiple coins</li>
                <li>• LocalStorage saves selected coins as array</li>
                <li>• Same styling classes and structure as current dropdowns</li>
                <li>• Dropdown stays open until user clicks outside or presses Escape</li>
            </ul>
        </div>
    </div>

    <script>
        // Simple demo functionality
        let isOpen = false;
        
        document.getElementById('multiSelectButton').addEventListener('click', function() {
            const dropdown = document.getElementById('multiSelectDropdown');
            isOpen = !isOpen;
            dropdown.style.display = isOpen ? 'block' : 'none';
        });

        document.addEventListener('click', function(e) {
            if (!e.target.closest('#multiSelectButton') && !e.target.closest('#multiSelectDropdown')) {
                document.getElementById('multiSelectDropdown').style.display = 'none';
                isOpen = false;
            }
        });

        function clearAll(checkbox) {
            if (checkbox.checked) {
                document.querySelectorAll('#multiSelectDropdown input[type="checkbox"]').forEach(cb => {
                    if (cb !== checkbox) cb.checked = false;
                });
            }
        }

        function removeCoin(coin) {
            alert(`Remove ${coin} filter`);
        }
    </script>
</body>
</html>
